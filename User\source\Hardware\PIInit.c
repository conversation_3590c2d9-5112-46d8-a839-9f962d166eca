/* --------------------------- (C) COPYRIGHT 2021 Fortiortech ShenZhen -----------------------------
    File Name      : PIInit.c
    Author         : Fortiortech  Appliction Team
    Version        : V1.0
	Editor         : Leo.li
    Date           : 2021-04-11
    Description    : This file contains PI initial function used for Motor Control.
----------------------------------------------------------------------------------------------------  
                                       All Rights Reserved
------------------------------------------------------------------------------------------------- */
/* Includes -------------------------------------------------------------------------------------*/
#include <MyProject.h>

/* -------------------------------------------------------------------------------------------------
    Function Name  : PI_Init
    Description    : PI参数初始化
    Date           : 2022-07-01
    Parameter      : None
------------------------------------------------------------------------------------------------- */
void PI_Init(void)
{
    PI1_KP          = SKP;
    PI1_KI          = SKI;
    PI1_EK1         =0;
    PI1_EK          =0;
    PI1_UKH         =0;
    PI1_UKL         =0;
    PI1_UKMAX       = SOUTMAX_Init;
    PI1_UKMIN       = SOUTMIN;
}
/**
    @brief        PI2用于母线限流
    @date         2022-07-14
*/
void PI2_Init(void)
{
    PI2_KP = _Q12(0.1);                //限制功率调节
    PI2_KI = _Q15(0.008);
	  PI2_UKMAX = SOUTMAX;
    PI2_UKMIN = SOUTMAX >>2;
	  PI2_UKH   = PI2_UKMAX;            //输出从最大开始调节
}
/**
    @brief        PI3用于弱磁增速
    @date         2022-07-14
*/
void PI3_Init(void)
{
//    PI3_KP          = AKP;
//    PI3_EK1         = 0;
//    PI3_EK          = 0;
//    PI3_KI          = AKI;
//    PI3_UKH         = 0;
//    PI3_UKL         = 0;
//    PI3_UKMAX       = A_Value(AMAX);
//    PI3_UKMIN       = A_Value(AMIN);
}

