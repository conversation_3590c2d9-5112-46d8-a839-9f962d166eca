#include <MyProject.h>

MCUTemperature  xdata TSDTemperature;

uint8 xdata TSD_Tempera[16] = {0x88, 0x89, 0x8A, 0x8B, 0x8C, 0x8D, 0x8E, 0x8F, 0x80, 0x81, 0x82, 0x83, 0x84, 0x85, 0x86, 0x87};
uint8 xdata TSD_ActualTempera[16] = {65, 70, 75, 80, 86, 91, 97, 103, 109, 115, 121, 128, 135, 142, 150, 0xFF};        //芯片温度
/* -------------------------------------------------------------------------------------------------
    Function Name  : TSD_Init
    Description    : 温度检测初始化
    Date           : 2022-02-18
    Parameter      : None
------------------------------------------------------------------------------------------------- */
void TSD_Init(void)
{
    ClrBit(TSD_CR, TSDEN);  //关闭保护使能
    TSDIE = 1;              //温度保护中断使能
    SetBit(TSD_CR, TSDADJ3);  //温度保护值设定
    SetBit(TSD_CR, TSDADJ2);
    ClrBit(TSD_CR, TSDADJ1);
    SetBit(TSD_CR, TSDADJ0);
    SetBit(IP0, PLVW1);   //中断优先级设置
    ClrBit(IP0, PLVW0);   //
	
	TSDTemperature.CheckCount = 0;
	TSDTemperature.ActualTemper = 65;
}

/* -------------------------------------------------------------------------------------------------
    Function Name  : TEMP_Check
    Description    : 温度检测
    Date           : 2022-02-18
    Parameter      : None
------------------------------------------------------------------------------------------------- */
void TEMP_Check(void)
{
    if (TSDTemperature.TSD_Enable_Flag)      //开启温度检测触发
    {
        if (TSDTemperature.TSD_Trigger_Flag)  //上次温度检测触发温度保护
        {
            if (TSDTemperature.CheckCount < 15)
            {
                TSDTemperature.CheckCount++;
                TSDTemperature.TSD_Trigger_Flag = 0;
            }
        }
        else
        {
            if (TSDTemperature.CheckCount > 0)
            {
                TSDTemperature.CheckCount--;
            }
        }
        
        TSDTemperature.TSD_Enable_Flag = 0;
        TSDTemperature.ActualTemper  = TSD_ActualTempera[TSDTemperature.CheckCount];
        TSD_CR = TSD_Tempera[TSDTemperature.CheckCount];    //使能温度检测，设置温度
    }
}