/**
 * @copyright (C) COPYRIGHT 2022 Fortiortech Shenzhen
 * @file      Customer.h
 * <AUTHOR>  Appliction Team
 * @note      Last modify author is <PERSON> He
 * @since     2019-05-17
 * @date      2022-07-14
 * @brief     This file contains customer parameter used for Motor Control.  
 */
 
/* Define to prevent recursive inclusion -------------------------------------------------------- */
#ifndef __CUSTOMER_H_
#define __CUSTOMER_H_
 /**********************基础参数*****************************
 1. 芯片参数
 2. 电机参数
 3. 电流采样参数
 4. 母线电压采样参数
  **************************************************************/
  
/* ----------------------------------------------------------------------------------------------------------------------------
                                             1.芯片参数                                                 
---------------------------------------------------------------------------------------------------------------------------- */
#define PWM_FREQUENCY                  (32.0)                                   ///< (kHz) 载波频率
#define PWM_DEADTIME                   (0.9)                                    ///< (us) 死区时间
#define MIN_WIND_TIME                  (1.8)                     ///< (us) 单电阻最小采样窗口，建议值死区时间+0.9us
#define DLL_TIME                       (PWM_DEADTIME + 0.3)                     ///< (us) 双电阻最小脉宽设置, 建议值为死区时间值+0.2us以上

/* ----------------------------------------------------------------------------------------------------------------------------
                                             2.电机参数                                                 
---------------------------------------------------------------------------------------------------------------------------- */
#define Pole_Pairs                      (1.0)                  ///< 极对数
#define LD                              (0.0022* 0.95)          ///< (H) D轴电感
#define LQ                              (0.0022* 0.95)          ///< (H) Q轴电感
#define RS                              (26.4*1.0)             ///< (Ω) 相电阻
#define Ke                              (1.0)                  ///< (V/KRPM) 反电动势常数，选择AO观测器不需要Ke
#define MOTOR_SPEED_BASE                (180000.0)             ///< (RPM) 速度基准

//// 若选择AO自适应观测器 则无需填写Ke
//#define KeVpp                          (1.832)                                 ///< (V)      反电动势测量的峰峰值
//#define KeT                            (68.40)                                 ///< (ms)     反电动势测量的周期
//#define Ke                             (Pole_Pairs * KeVpp * KeT / 207.84)     ///< (V/KRPM) 反电动势常数

/* ----------------------------------------------------------------------------------------------------------------------------
                                             3.电流采样参数                                                   
---------------------------------------------------------------------------------------------------------------------------- */
/**
 * @breaf  运放模式选择
 * @param (AMP_NOMAL)       外部放大
 * @param (AMP_PGA_DUAL)    内部PGA双端差分输入(放大倍数的标定外部引脚串1kΩ电阻)
 */
#define HW_AMP_MODE                     (AMP_PGA_DUAL)  ///< 运放模式选择       

#define HW_RSHUNT                       (0.1)         ///< (Ω)  采样电阻                               

/**
 * 放大倍数设置
 * @param (AMP2x)       内部PGA放大2倍
 * @param (AMP4x)       内部PGA放大4倍
 * @param (AMP8x)       内部PGA放大8倍
 * @param (AMP16x)      内部PGA放大16倍
 * @param (xxxxxx)      外部放大模式填写相应倍数
 */
#define HW_AMPGAIN                      (AMP4x)         ///< 放大倍数设置                       

/**
 * 参考电压设置
 * @param (VREF3_0)       参考电压设置为3.0V
 * @param (VREF4_0)       参考电压设置为4.0V
 * @param (VREF4_5)       参考电压设置为4.5V
 * @param (VREF5_0)       参考电压设置为5.0V
 */
#define HW_ADC_VREF                    (VREF5_0)        ///< (V)  ADC参考电压

/**
 * 电流采样模式选择
 * @param (Single_Resistor)       单电阻电流采样模式
 * @param (Double_Resistor)       双电阻电流采样模式
 * @param (Three_Resistor)        三电阻电流采样模式
 */
#define Shunt_Resistor_Mode            (Single_Resistor)    ///< 电流采样模式选择

/**
 * 基准电压VREF对外输出使能
 * @param (Disable)      禁止
 * @param (Enable)       使能
 */
#define VREF_OUT_EN                     (Disable)         ///< 基准电压VREF对外输出使能

/**
 * 偏置电压设置
 * @param (VHALF1_8)        VHALF电压设置为1/8VREF
 * @param (VHALF1_4)        VHALF电压设置为1/4VREF
 * @param (VHALF25_64)      VHALF电压设置为25/64VREF
 * @param (VHALF1_2)        VHALF电压设置为1/2VREF
 */
#define HW_VHALF_SEL                    (VHALF1_2)      ///< 偏置电压设置

/**
 * @brief  运放0偏置电压配置（根据实际电路匹配）
 * @param (Enable)       运放0有接VHALF
 * @param (Disable)      运放0没接VHALF
 */
#define VHALF_EN                        (Enable)        ///< VHALF输出使能

/* ----------------------------------------------------------------------------------------------------------------------------
                                             4.母线电压采样参数                                                  
---------------------------------------------------------------------------------------------------------------------------- */

#define RV1                             (940.0)          ///< (kΩ) 母线电压分压电阻1
#define RV2                             (6.8)             ///< (kΩ) 母线电压分压电阻2


 /**********************电机运行参数*****************************
 1.  预充电参数与IPMtest
 2.  预定位参数
 3.  启动参数
 4.  环路参数 
 5.  调速开关模式 
 6.  顺逆风参数 
 7.  观测器模式   
 8.  过调制
 9.  正反转模式  
 10. PWM调速开关参数
 11. 启停测试参数   
 12.发热丝控制参数设置
 13.便于仿真，设置复用烧录口FK、FD端口控制使能
 14.风速开关选择  
 15.掉电记忆  
  **************************************************************/
  
/* ----------------------------------------------------------------------------------------------------------------------------
                                            1.预充电参数                                                  
---------------------------------------------------------------------------------------------------------------------------- */
/**
 * 预充电使能
 * @param (Disable)       禁止
 * @param (Enable)        使能
 */
#define CHARGE_EN                      (Enable)          ///< 预充电使能
#define CHARGE_DUTY                    (0.1)            ///< (%)  预充电下桥Duty
#define CHARGE_TIME                    (80)             ///< (ms) 预充电时间 
/**
 * IPM测试模式，用以检测MCU——MOS间电路是否正常(使用此功能需要使能预充电，禁止接电机)
 * @param (Disable)       禁止
 * @param (Enable)        使能
 */
 #define IPMTEST                       (Disable)
/* ----------------------------------------------------------------------------------------------------------------------------
                                          2.预定位参数                                                    
---------------------------------------------------------------------------------------------------------------------------- */
/**
 * 预定位模式选择
 * @param (ALIGN_DSIABLE)       禁止
 * @param (ALIGN_NOMAL)         正常预定位
 * @param (ALIGN_TEST)          测试模式，可用于手动测试电机极对数
 */
#define ALIGN_MOME                     (ALIGN_DSIABLE)  ///< 预定位模式选择

#define Align_Angle                    (0.0)          ///< (°) 预定位角度

#define Align_Time                     (1)              ///< (ms) 预定位时间 

/* 预定位的Kp、Ki */
#define DQKP_Alignment                 _Q12(1.0)        ///< 预定位的KP
#define DQKI_Alignment                 _Q15(0.01)       ///< 预定位的KI
#define ID_Align_CURRENT               I_Value(0.0)     ///< (A) D轴定位电流
#define IQ_Align_CURRENT               I_Value(2.8)     ///< (A) Q轴定位电流

/* ----------------------------------------------------------------------------------------------------------------------------
                                            3.启动参数                                                   
---------------------------------------------------------------------------------------------------------------------------- */
/**
 * 开环启动模式选择
 * @param (Open_Start)          开环强拖启动
 * @param (Omega_Start)         Omega启动
 */
#define Open_Start_Mode                (Omega_Start)    ///< 开环启动模式选择

/* 启动电流 */
#define ID_Start_CURRENT               I_Value(0.0)     ///< (A) D轴启动电流
#define IQ_Start_CURRENT               I_Value(0.82)    ///< (A) Q轴启动电流

/* 顺风启动切入闭环电流  */
#define ID_RUN_CURRENT                 I_Value(0.0)     ///< (A) D轴运行电流
#define IQ_RUN_CURRENT                 I_Value(0.82)     ///< (A) Q轴运行电流

/* 启动ATO参数 */
#define ATO_BW_START                   (0.0)            ///< 观测器带宽的滤波值，经典值为1.0-200.0 
#define ATO_BW_RUN1                    (160.0) 
#define ATO_BW_RUN2                    (190.0) 
#define ATO_BW_RUN3                    (220.0) 
#define ATO_BW_RUN4                    (300.0) 

/* 启动ATO爬坡时间控制 */
#define ATO_START_HOLDTIME             (10)               ///< (ms)观测器带宽的滤波值,启动第一拍ATO持续时间
#define ATO_RAMP_PERIOD                (20)               ///< (ms)观测器带宽的滤波值,ATO爬坡递增 间隔时间


/* OMEGA启动参数 */
#define MOTOR_OMEGA_RAMP_ACC           (30)               ///< omega启动的增量（每个载波递增值） 
#define MOTOR_OMEGA_RAMP_MIN           S_Value(7000.0)      ///< (RPM) omega启动的最小切换转速
#define MOTOR_OMEGA_RAMP_END           S_Value(9000.0)     ///< (RPM) omega启动的限制转速

#define MOTOR_SPEED_SMOMIN_RPM         (6000.0)            ///< (RPM) SMO运行最小转速影响启动
#define SPD_BW                         (20.0)             ///< 速度带宽的滤波值，经典值为5.0-40.0
#define MOTOR_LOOP_RPM                 S_Value(11000.0)     ///< (RPM) 启动后电流环切入外部环路转速阈值

/* 电机运行时最大最小转速 */
#define MOTOR_SPEED_MIN_RPM            S_Value(40000.0)        ///< (RPM) 运行最小转速
#define MOTOR_SPEED_MAX_RPM            S_Value(105000.0)       ///< (RPM) 运行最大转速
#define MOTOR_SPEED_STOP_RPM           S_Value(60000.0)       ///< (RPM) 运行最小转速

/* -----------电流环参数设置值-------------------------------------------------------------- */
#define DKP_Start                      _Q12(0.3)        ///< 启动DQ轴电流环KP
#define DKI_Start                      _Q15(0.005)       ///< 启动DQ轴电流环KI
#define QKP_Start                      _Q12(0.3)        ///< 启动DQ轴电流环KP
#define QKI_Start                      _Q15(0.005)       ///< 启动DQ轴电流环KI

#define DKP                            _Q12(2.5)        ///< 运行DQ轴电流环KP
#define DKI                            _Q15(0.6)       ///< 运行DQ轴电流环KI
#define QKP                            _Q12(2.5)        ///< 运行DQ轴电流环KP
#define QKI                            _Q15(0.6)       ///< 运行DQ轴电流环KI
/* D轴参数设置 */
#define DOUTMAX                        _Q15(0.4)        ///< D轴电压最大限幅值，单位：输出占空比
#define DOUTMIN                        _Q15(-0.4)       ///< D轴电压最小限幅值，单位：输出占空比
/* Q轴参数设置 */
#define QOUTMAX                        _Q15(0.95)       ///< Q轴电压最大限幅值，单位：输出占空比
#define QOUTMIN                        _Q15(-0.95)      ///< Q轴电压最小限幅值，单位：输出占空比

/* ----------------------------------------------------------------------------------------------------------------------------
                                        4. 环路参数                                                
---------------------------------------------------------------------------------------------------------------------------- */
/**
 * 闭环方式选择
 * @param (CURRENT_LOOP_CONTROL)       电流环
 * @param (SPEED_LOOP_CONTROL)         速度环
 * @param (POWER_LOOP_CONTROL)         功率环
 * @param (UQ_LOOP_CONTROL)            UQ环
 */
#define MOTOR_CTRL_MODE                (SPEED_LOOP_CONTROL) ///< 闭环方式选择

#define LOOP_TIME                       (2)                 ///< (ms) 外环环调节周期,默认为 1ms

#define SKP                            _Q12(0.2)            ///< 外环KP     
#define SKI                            _Q15(0.002)          ///< 外环KI
#define SKD                            _Q12(0.0)            ///< 外环KD

#define SOUTMAX_Init                   I_Value(0.7)         ///< (A) 初始外环电流最大限幅值
#define SOUTMAX                        I_Value(1.0)         ///< (A) 外环电流最大限幅值
#define SOUTMIN                        I_Value(0.0)         ///< (A) 外环电流最小限幅值

#define RAMP_INC                       S_Value(500)         ///< (RPS) 每秒爬坡递增量
#define RAMP_DEC                       S_Value(500)         ///< (RPS) 每秒爬坡递减量

  /* -----速度控制挡位----- */
#define Motor_Speed_Low                S_Value(75000)       ///< 低挡转速
#define Motor_Speed_Mid                S_Value(92000)       ///< 中挡转速
#define Motor_Speed_HIgh               S_Value(103000)      ///< 高挡转速

/**
 * 五段式七段式切换
 * @param (Disable)       禁止
 * @param (Enable)        使能
 */
#define SVPWM_5_Segment_Run_Enale (Disable) // 0:不开启五段式子 1：开启五段式

#define Motor_F5SEG_Speed S_Value(67500) // 速度大于这个后切换成五段式
#define Motor_F7SEG_Speed S_Value(60500) // 速度低于这个值，切换成七段式，主要是起动的时候



/* 限制功率 */
#define POWERLPFLIMIT                  (8600)              ///< 15800大概在110W左右

/**
 * 弱磁设置
 * @param (Disable)       禁止
 * @param (Enable)        使能
 */
#define MotorFiledWeakenEn            (Disable)                  ///<  弱磁使能位
#define MotorFiledWeakenUs            _Q15(0.90)                ///<  弱磁能到的最大饱和电压(弱磁转速上不去的时候可以调小该值)

#define MotorFiledWeakenKp            _Q12(0.2)                 ///<  弱磁控制的Kp
#define MotorFiledWeakenKi            _Q15(0.003)               ///<  弱磁控制的Ki

/**
 * 电压补偿
 * @param (Disable)      禁止
 * @param (Enable)       使能
 */
#define VoltageCompensationEn           (Enable)
#define VoltageCompensationDelayCnt     (200)                              ///< 进入速度环后延迟1S进入电压补偿
#define LinearCompensationAngel          _Q15(15.0 / 180.0)               ///< 根据电压降低到AC210V时开始给补偿角度
#define LinearCompensationAngel_MAX      _Q15(25.0 / 180.0)              ///< 电压降低时最大补偿角度
#define LinearCompensationAngel_MIN      _Q15(-25.0 / 180.0)             ///< 电压降低时最小补偿角度

/**
 * 线性降转速
 * @param (Disable)      禁止
 * @param (Enable)       使能
 */
#define LinearDecelerationEN  (Enable)  

#define Upper_LimitVoltage		(264.0)  ///< 实际输入AC 210V,理论上对应DC297V，但实际只有DC264V左右（实测为准,DEMO实测数据为DC264V左右）
#define Lower_LimitVoltage    (223.0)  ///< 实际输入AC 180V,理论上对应DC254V，但实际只有DC223V左右（实测为准，DEMO实测数据为DC223V左右）
#define Upper_LimitSpeed	    (103000) ///< AC 210V开始降转速的速度初始值
#define Lower_LimitSpeed	    (90000)  ///< AC 180V降到最低转速的速度值
#define Voltage_K					    ((float)S_Value(Upper_LimitSpeed - Lower_LimitSpeed) / (float)(_Q15((Upper_LimitVoltage-Lower_LimitVoltage)/ HW_BOARD_VOLT_MAX)))


/**
 * 过零丢失判断
 * 用于插拔电的快速开关机
 * @param (Disable)       禁止
 * @param (Enable)        使能
 */
#define AC_ZeroCrossing_MODE            (Enable)

/* ----------------------------------------------------------------------------------------------------------------------------
                                        5. 调速开关模式                                                   
---------------------------------------------------------------------------------------------------------------------------- */
/**
 * 闭环方式选择
 * @param (PWMMODE)       PWM调速
 * @param (SREFMODE)      模拟调速
 * @param (NONEMODE)      直接给定值，不调速
 * @param (KEYSCANMODE)   按键控制模式
 * @param (ONOFFTEST)     启停测试工具
 */
#define SPEED_MODE                     (KEYSCANMODE)           ///< 闭环方式选择  

/* ----------------------------------------------------------------------------------------------------------------------------
                                          6. 顺逆风参数                                                   
---------------------------------------------------------------------------------------------------------------------------- */
/**
 * 顺逆风检测方式
 * @param (NoTailWind)      无逆风顺风判断
 * @param (RSDMethod)       RSD比较器方法
 * @param (BEMFMethod)      BEMF比较器方法
 * @param (FOCMethod)       FOC计算方法
 */
#define TAILWIND_MODE                  (NoTailWind)     ///< 顺逆风检测方式

/* 逆风顺风电流环KP、KI */
#define DQKP_TailWind                  _Q12(1.5)        ///< FOC计算顺逆风检测电流环KP
#define DQKI_TailWind                  _Q15(0.5)        ///< FOC计算顺逆风检测电流环KI

#define TAILWIND_TIME                  (10000)           ///< (ms) 顺逆风检测时间 
#define ATO_BW_WIND                    (200)            ///< 逆风判断观测器带宽的滤波值，经典值为8.0-100.0 
#define SPD_BW_WIND                    (20.0)           ///< 逆风判断速度带宽的滤波值，经典值为5.0-40.0 

/* ----------------------------------------------------------------------------------------------------------------------------
                                          7. 观测器模式                                                 
---------------------------------------------------------------------------------------------------------------------------- */
/**
 * 估算器模式选择
 * @param (SMO)       滑膜估算
 * @param (PLL)       锁相环
 * @param (AO)        自适应
 */
#define EstimateAlgorithm              (AO)                 ///< 估算器模式选择

/* ----------------------------------------------------------------------------------------------------------------------------
                                          8. 过调制                                                
---------------------------------------------------------------------------------------------------------------------------- */
/**
 * 过调制
 * @param (Disable)      禁止
 * @param (Enable)       使能
 */
#define OverModulation                 (Enable)             ///< 开启过调制UD,UQ会被放大1.15倍，但极限状态可能导致电流畸变                                  

/* ----------------------------------------------------------------------------------------------------------------------------
                                          9. 正反转模式                                                
---------------------------------------------------------------------------------------------------------------------------- */
/**
 * 转向设置
 * @param (CW)        顺时针
 * @param (CCW)       逆时针
 */
#define FR_MODE                        (CCW)                 ///< 转向设置 

/* ----------------------------------------------------------------------------------------------------------------------------
                                          10. PWM调速开关参数                                                
---------------------------------------------------------------------------------------------------------------------------- */
/**
 * PWM调速 PWM极性选择
 * @param (PosiPWMDUTY)       正逻辑
 * @param (NegaPWMDUTY)       反逻辑
 */
#define PWMDUTY_POLARITY               (PosiPWMDUTY)        ///< PWM调速 PWM极性选择   

/* 开关机Duty设置 */
#define OFFPWMDuty                     _Q15(0.07)           ///< 关机PWM占空比，小于该占空比关机
#define OFFPWMDutyHigh                 _Q15(1.0)            ///< 关机PWM占空比，大于该占空比关机
#define ONPWMDuty                      _Q15(0.08)           ///< 开机PWM占空比，大于该占空比时开机
#define MINPWMDuty                     _Q15(0.10)           ///< 速度曲线上最小PWM占空比
#define MAXPWMDuty                     _Q15(0.90)           ///< 速度曲线上最大PWM占空比

/* ----------------------------------------------------------------------------------------------------------------------------
                                          11. 启停测试参数                                                
---------------------------------------------------------------------------------------------------------------------------- */
/* -----启停测试参数配置----- */
#define ONOFFTEST_REF                  S_Value(105000)        ///< 测试给定，注意闭环方式不同给定值需要更改
#define ONOFFTEST_ON_TIME              (3000)               ///< (ms) 启动运行时间
#define ONOFFTEST_OFF_TIME             (3000)               ///< (ms) 停止时间

/* -----停机刹车选择----- */
#define StopBrakeFlag                  (1)
#define StopWaitTime                   (550)                ///< (ms) 刹车等待时间
#define Stop_MOTOR_SPEED_RPM            S_Value(65000)        ///< (RPM) 小于该转速刹车
#endif
/* ----------------------------------------------------------------------------------------------------------------------------
                                             12.发热丝控制参数设置                                                 
---------------------------------------------------------------------------------------------------------------------------- */
  /* ----- 温度控制挡位----- */
 #define Temperature_Off               (0x00)                  ///< 关闭温度
 #define Temperature_Control_Period    (20)                    ///< 温度控制周期

 #define Speed_LowTemperature_LOW      (8)                     ///< 低转速发热
 #define Speed_LowTemperature_Mid      (10)
 #define Speed_LowTemperature_High     (15)
 
 #define Speed_MidTemperature_LOW      (10)                    ///< 中速发热
 #define Speed_MidTemperature_Mid      (15)
 #define Speed_MidTemperature_High     (18)
 
 #define Speed_HIghTemperature_LOW     (10)                    ///< 高速发热
 #define Speed_HIghemperature_Mid      (18)
 #define Speed_HIghTemperature_High    (20)
 
 
 #define Temperature_ADCLOW            (13000)                 ///< NTC60度电阻值24K
 #define Temperature_ADCMid            (8000)                  ///< NTC80度电阻值12K
 #define Temperature_ADCHigh           (5200)                  ///< NTC100度电阻值6.6K

/**
 * 恒温控制
 * @param (Disable)       禁止
 * @param (Enable)        使能
 */
 #define ThermostaticENABLE            (Disable)               ///< 恒温控制使能
 
/**
 * NTC采集
 * @param (Disable)       禁止
 * @param (Enable)        使能
 */
 #define NTCSignalENABLE            (Enable)               ///<NTC信号采集，1：复用LED端口采集NTC信号  0：不使能LED端口复用，没有NTC的可直接关掉


/* ----------------------------------------------------------------------------------------------------------------------------
                                             13.便于仿真，设置复用烧录口FK、FD端口控制使能                                                
---------------------------------------------------------------------------------------------------------------------------- */ 
 /**
 * 过零信号采集
 * @param (Disable)       禁止
 * @param (Enable)        使能
 */
 #define ZeroCrossSignalENABLE            (Enable)     ///<NTC信号采集，1：复用FICEK端口采集过零信号  0：不使能过零信号采集
                                                       ///<  注意：  烧录口FK作为过零信号采集，一般接有电容，会导致无法下载程序，调试阶段可先断开过零信号采集和断开电容                                                       
 /**
 * 发热丝控制信号
 * @param (Disable)       禁止
 * @param (Enable)        使能
 */
 #define HeatingControlSignalENABLE            (Enable)     ///<NTC信号采集，1：复用FICED端口作为发热丝控制信号  0：不使能控制信号
                                                            ///<  注意 ： 烧录口FD复用为发热丝控制信号，有可能影响程序下载
																														
/* ----------------------------------------------------------------------------------------------------------------------------
                                             14.风速开关选择                                              
---------------------------------------------------------------------------------------------------------------------------- */ 
 /**
 * 风速开关选择
 * @param (HighVoltageSwitch)       高压开关
 * @param (LowVoltageSwitch)        低压开关
 */
 #define SwitchMode            (LowVoltageSwitch)     
    
/* ----------------------------------------------------------------------------------------------------------------------------
                                             15.掉电记忆
---------------------------------------------------------------------------------------------------------------------------- */
/**
 * 掉电记忆
 * @param (Disable)       禁止
 * @param (Enable)        使能
 */
#define PowerDownMemory_EN               (Enable)

