/**
 * @copyright (C) COPYRIGHT 2022 Fortiortech Shenzhen
 * @file      SMDU.h
 * <AUTHOR> He
 * @note      Last modify author is <PERSON>
 * @since     2022-07-14
 * @date      2022-07-14
 * @brief       
 */
 

#ifndef __S_M_D_U_H_
#define __S_M_D_U_H_

#include <FU68xx_5.h>

/**
 * @brief SMDU的模式类型
 *
 * @note 使用@ref SMDU_RunNoBlock时, 其中的mode参数可以直接使用本枚举的内容
 * @note 使用@ref SMDU_RunBlock时, 其中的mode参数可以直接使用本枚举的内容
 */
typedef enum
{
    S1MUL   = 0, /**< 有符号乘法, 计算结果左移1位 */
    SMUL    = 1, /**< 有符号乘法 */
    UMUL    = 2, /**< 无符号乘法 */
    DIV     = 3, /**< 32/16无符号除法 */
    SIN_COS = 4, /**< Sin/Cos */
    ATAN    = 5, /**< ATan */
    LPF     = 6, /**< 低通滤波 */
    PI      = 7  /**< PI */
} ETypeSMDUMode;
/******************************************************************************///External Symbols
/******************************************************************************///External Function
/**
 * @brief 运行SMDU且不等待运行结束
 *
 * @param  stan (0-3) 要启动的计算单元编号
 * @param  mode (0-7) 指定计算单元的模式, 可使用@ref ETypeSMDUMode 作为计算模式的设置\n
 * @ref S1MUL   有符号乘法, 计算结果左移1位 \n
 * @ref SMUL    有符号乘法 \n
 * @ref UMUL    无符号乘法 \n
 * @ref DIV     32/16无符号除法 \n
 * @ref SIN_COS Sin/Cos \n
 * @ref ATAN    ATan \n
 * @ref LPF     低通滤波 \n
 * @ref PI      PI \n
 */
#define SMDU_RunNoBlock(stan, mode)   do                                                  \
                                      {                                                   \
                                          MDU_CR = MDUSTA0 << stan | (unsigned char)mode; \
                                      } while (0)

/**
 * @brief 运行SMDU且等待运行结束
 *
 * @param  stan (0-3) 要启动的计算单元编号
 * @param  mode (0-7) 指定计算单元的模式, 可使用@ref ETypeSMDUMode 作为计算模式的设置\n
 * @ref S1MUL   有符号乘法, 计算结果左移1位 \n
 * @ref SMUL    有符号乘法 \n
 * @ref UMUL    无符号乘法 \n
 * @ref DIV     32/16无符号除法 \n
 * @ref SIN_COS Sin/Cos \n
 * @ref ATAN    ATan \n
 * @ref LPF     低通滤波 \n
 * @ref PI      PI \n
 */
#define SMDU_RunBlock(stan, mode)   do                                       \
                                    {                                        \
                                        SMDU_RunNoBlock(stan, mode);         \
                                        while (MDU_CR & MDUBSY);             \
                                    } while (0);





/* SMDU控制 */
extern int16  HW_One_PI(int16 Xn1);
extern int16  HW_One_PI2(int16 Xn1);
extern int16  HW_One_PI3(int16 Xn1);
extern int16  LPFFunction(int16 Xn1, int16 Xn0, int8 K);
extern int16  Atan_Us_MDU(int16 Xn1, int16 Xn0);
extern int16  DivQ_L_MDU(uint16 Xn1, uint16 Xn0,uint16 Yn);

extern uint16 Sqrt_alpbet(int16 i_alp, int16 i_bet);
#endif

