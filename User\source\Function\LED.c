/*  -------------------------- (C) COPYRIGHT 2022 Fortiortech ShenZhen ---------------------------*/
/**
 * @copyright (C) COPYRIGHT 2022 Fortiortech Shenzhen
 * @file      LED.c
 * <AUTHOR>  Appliction Team
 * @since     Create:2022-07-14
 * @date      Last modify:2022-07-18
 * @note      Last modify author is <PERSON>.li
 * @brief
 */

/* Includes -------------------------------------------------------------------------------------*/
#include <FU68xx_5.h>
#include <Myproject.h>

/* Private variables ----------------------------------------------------------------------------*/
LEDCtl_TypeDef LEDCtl;

volatile uint16 Temperature_Low[3] = {14245, 13636, 12750}; //50°
volatile uint16 Temperature_Mid[3] = {9116, 8453, 7832};  //70
volatile uint16 Temperature_High[3] = {5445, 5033, 4652}; //90



/**
 * @brief      LED控制，目标速度和目标温度赋值
 * @date       2022-07-14
 */
void LEDControl(void)
{
    if ((mcState != mcFault))
    {
        if ((KS.KeyValuetotal & 0x80) == 0)                    //SW3 没有按下
        {
            switch (KS.KeyValuetotal & 0x70)
            {
                case 0x10:                          //判定转速
                    switch (KS.KeyValuetotal & 0x0F)          //判定温度
                    {
                        case 0x01:
//                            D1 = 1;
//                            D2 = 1;
//                            D3 = 1;
                            User.ScrOnAngleTime  = Temperature_Off;
												    GP01=1;//关闭发热丝
                            break;
                            
                        case 0x02:
//                            D1 = 1;
//                            D2 = 1;
//                            D3 = 0;
                            User.ScrOnAngleTime   = Speed_LowTemperature_LOW;
                            break;
                            
                        case 0x04:
//                            D1 = 1;
//                            D2 = 0;
//                            D3 = 0;
                            User.ScrOnAngleTime  = Speed_LowTemperature_Mid;
                            break;
                            
                        case 0x08:
//                            D1 = 0;
//                            D2 = 0;
//                            D3 = 0;
                            User.ScrOnAngleTime   = Speed_LowTemperature_High;
                            break;
                            
                        default:
                            break;
                    }
                    
//                    D4 = 0;
//                    D5 = 1;
//                    D6 = 1;
                    mcFocCtrl.Ref = Motor_Speed_Low;         // 转速赋值
                    break;
                    
                case 0x20:
                    switch (KS.KeyValuetotal & 0x0F)
                    {
                        case 0x01:
//                            D1 = 1;
//                            D2 = 1;
//                            D3 = 1;
                            User.ScrOnAngleTime  = Temperature_Off;
												    GP01=1;//关闭发热丝
                            break;
                            
                        case 0x02:
//                            D1 = 1;
//                            D2 = 1;
//                            D3 = 0;
                            User.ScrOnAngleTime  = Speed_MidTemperature_LOW;
                            break;
                            
                        case 0x04:
//                            D1 = 1;
//                            D2 = 0;
//                            D3 = 0;
                            User.ScrOnAngleTime  = Speed_MidTemperature_Mid;
                            break;
                            
                        case 0x08:
//                            D1 = 0;
//                            D2 = 0;
//                            D3 = 0;
                            User.ScrOnAngleTime  = Speed_MidTemperature_High;
                            break;
                            
                        default:
                            break;
                    }
                    
//                    D4 = 0;
//                    D5 = 0;
//                    D6 = 1;
                    mcFocCtrl.Ref   = Motor_Speed_Mid;           // 转速赋值
                    break;
                    
                case 0x40:
                    switch (KS.KeyValuetotal & 0x0F)
                    {
                        case 0x01:
//                            D1 = 1;
//                            D2 = 1;
//                            D3 = 1;
                            User.ScrOnAngleTime  = Temperature_Off;
												    GP01=1;//关闭发热丝
//                            GP10 = 1;
//                            GP11 = 1;
                            break;
                            
                        case 0x02:
//                            D1 = 1;
//                            D2 = 1;
//                            D3 = 0;
                            User.ScrOnAngleTime  = Speed_HIghTemperature_LOW;
                            break;
                            
                        case 0x04:
//                            D1 = 1;
//                            D2 = 0;
//                            D3 = 0;
                            User.ScrOnAngleTime  = Speed_HIghemperature_Mid;
                            break;
                            
                        case 0x08:
//                            D1 = 0;
//                            D2 = 0;
//                            D3 = 0;
                            User.ScrOnAngleTime  = Speed_HIghTemperature_High;
                            break;
                            
                        default:
                            break;
                    }
                    
//                    D4 = 0;
//                    D5 = 0;
//                    D6 = 0;
                    mcFocCtrl.Ref = Motor_Speed_HIgh;           // 转速赋值
                    break;
                    
                default:
                    break;
            }
            
            //温度赋值控制
            #if(ThermostaticENABLE)
            {
                switch (KS.KeyValuetotal & 0x0F)
                {
                    case 0x01:
                        User.ScrOnAngleTime  = Temperature_Off;
                        break;
                        
                    case 0x02:
                        User.TemperatureDatum[0] = Temperature_Low[0];
                        User.TemperatureDatum[1] = Temperature_Low[1];
                        User.TemperatureDatum[2] = Temperature_Low[2];
                        break;
                        
                    case 0x04:
                        User.TemperatureDatum[0] = Temperature_Mid[0];
                        User.TemperatureDatum[1] = Temperature_Mid[1];
                        User.TemperatureDatum[2] = Temperature_Mid[2];
                        break;
                        
                    case 0x08:
                        User.TemperatureDatum[0] = Temperature_High[0];
                        User.TemperatureDatum[1] = Temperature_High[1];
                        User.TemperatureDatum[2] = Temperature_High[2];
                        break;
                        
                    default:
                        break;
                }
            }
            #endif
        }
        else
        {
//            D1 = 1;
//            D2 = 1;
//            D3 = 1;
            User.ScrOnAngleTime  = Temperature_Off;
					  GP01=1;//关闭发热丝
            
            switch (KS.KeyValuetotal & 0x70)
            {
                case 0x10:
//                    D4 = 0;
//                    D5 = 1;
//                    D6 = 1;
                    mcFocCtrl.Ref = Motor_Speed_Low;              // 转速赋值
                    break;
                    
                case 0x20:
//                    D4 = 0;
//                    D5 = 0;
//                    D6 = 1;
                    mcFocCtrl.Ref = Motor_Speed_Mid;             // 转速赋值
                    break;
                    
                case 0x40:
//                    D4 = 0;
//                    D5 = 0;
//                    D6 = 0;
                    mcFocCtrl.Ref = Motor_Speed_HIgh;            // 转速赋值
                    break;
                    
                default:
                    break;
            }
        }
    }
    
    User.ScrOnPeriod          = Temperature_Control_Period;
    User.ScrOnAngleTimeDatum  = User.ScrOnAngleTime;
}


/**
 * @brief        功能函数，温度闭环控制
 * @date         2022-07-14
 */
void Temperature_Control(void)
{
    #if(ThermostaticENABLE ==1)
    {
		static uint8 Delay = 0;                   //延迟时间
		
        if (Delay > 0)
        {
            Delay--;
        }
        
        if ((Delay == 0) && ((User.TPCtrlDealy == 0)))
        {
            if (mcFocCtrl.NTCValueFlt< User.TemperatureDatum[2])
            {
                if (User.ScrOnAngleTimeDatum > 0)
                {
                    User.ScrOnAngleTimeDatum --;
                    Delay = 15;
                }
            }
            else if (mcFocCtrl.NTCValueFlt> User.TemperatureDatum[0])
            {
                if (User.ScrOnAngleTimeDatum < User.ScrOnPeriod)
                {
                    User.ScrOnAngleTimeDatum ++;
                    
                    if (User.ScrOnAngleTimeDatum > User.ScrOnAngleTime)
                    {
                        Delay = 5;
                    }
                    else
                    {
                        Delay = 1;
                    }
                }
            }
        }
    }
    #else
    {
        if (mcFocCtrl.mcDcbusFlt >= 24500)  //随电压变化功率
        {
            User.ScrOnAngleTimeDatum = User.ScrOnAngleTime - 2;
        }
        else if (mcFocCtrl.mcDcbusFlt >= 23400)
        {
            User.ScrOnAngleTimeDatum = User.ScrOnAngleTime - 1;
        }
        else if (mcFocCtrl.mcDcbusFlt >= 21400)
        {
            User.ScrOnAngleTimeDatum = User.ScrOnAngleTime;
        }
        else if (mcFocCtrl.mcDcbusFlt >= 19600)
        {
            User.ScrOnAngleTimeDatum = User.ScrOnAngleTime + 1;
        }
        else
        {
            User.ScrOnAngleTimeDatum = User.ScrOnAngleTime + 2;
        }
    }
    #endif
}

#define LED_Time  20
//uint8 LED_Time = 20;
void LedDisplay(uint8 uLedMask)
{
	    LEDCtl.LEDCnt++;
		GP12 = 0;
		GP16 = 0;
		GP21 = 0;	
	
		if(LEDCtl.LEDCnt<=(LED_Time*1))
		{
			if(uLedMask & LED1_MASK) //if(LED1==1)  //D11
			{
				SetBit(P1_OE, P12); 
				ClrBit(P1_OE, P16); 	
				SetBit(P2_OE, P21);
				GP12 = 1;
				GP16 = 0;
				GP21 = 0;
							
			}	
			else
			{
				SetBit(P1_OE, P12); 
				SetBit(P1_OE, P16); 	
				SetBit(P2_OE, P21);
				GP12 = 0;
				GP16 = 0;
				GP21 = 0;					
				
			}				
		}
		else if (LEDCtl.LEDCnt<=(LED_Time*2))
		{
			if (uLedMask & LED2_MASK) //if(LED2==1)   //D8
			{
				SetBit(P1_OE, P12); 
				ClrBit(P1_OE, P16); 	
				SetBit(P2_OE, P21);
				GP12 = 0;
				GP16 = 0;
				GP21 = 1;
			}	
			else
			{
				SetBit(P1_OE, P12); 
				SetBit(P1_OE, P16); 	
				SetBit(P2_OE, P21);
				GP12 = 0;
				GP16 = 0;
				GP21 = 0;				
				
			}					
		}			
		else if (LEDCtl.LEDCnt<=(LED_Time*3))
		{
			if (uLedMask & LED3_MASK) //if(LED3==1)   //D7
			{
				ClrBit(P1_OE, P12); 
				SetBit(P1_OE, P16); 	
				SetBit(P2_OE, P21);
				GP12 = 0;
				GP16 = 0;
				GP21 = 1;
						
			}	
			else
			{
				SetBit(P1_OE, P12);  
				SetBit(P1_OE, P16); 	
				SetBit(P2_OE, P21);
				GP12 = 0;
				GP16 = 0;
				GP21 = 0;				
			}					
		}	
		else if (LEDCtl.LEDCnt<=(LED_Time*4))
		{
			if (uLedMask & LED4_MASK) //if(LED4==1)  //D10
			{
				ClrBit(P1_OE, P12);  
				SetBit(P1_OE, P16); 	
				SetBit(P2_OE, P21);
				GP12 = 0;
				GP16 = 1;
				GP21 = 0;
							
			}	
			else
			{
				SetBit(P1_OE, P12);  
				SetBit(P1_OE, P16); 	
				SetBit(P2_OE, P21);
				GP12 = 0;
				GP16 = 0;
				GP21 = 0;					
				
			}					
		}			
		else if (LEDCtl.LEDCnt<=(LED_Time*5))
		{
			if (uLedMask & LED5_MASK) //if(LED5==1) //D9
			{
				SetBit(P1_OE, P12); 
				SetBit(P1_OE, P16); 	
				ClrBit(P2_OE, P21);
				GP12 = 1;
				GP16 = 0;
				GP21 = 0;
			}	
			else
			{
				SetBit(P1_OE, P12); 
				SetBit(P1_OE, P16); 	
				SetBit(P2_OE, P21);
				GP12 = 0;
				GP16 = 0;
				GP21 = 0;					
				
			}					
		}	
		else if (LEDCtl.LEDCnt<=(LED_Time*6))
		{
			if (uLedMask & LED6_MASK) //if(LED6==1) //D6
			{
				SetBit(P1_OE, P12);  
				SetBit(P1_OE, P16); 	
				ClrBit(P2_OE, P21);
				GP12 = 0;
				GP16 = 1;
				GP21 = 0;
							
			}	
			else
			{
				SetBit(P1_OE, P12); 
				SetBit(P1_OE, P16); 	
				SetBit(P2_OE, P21);
				GP12 = 0;
				GP16 = 0;
				GP21 = 0;				
				
			}					
		}
		else if ((LEDCtl.LEDCnt <= (LED_Time*6+1))&&(mcFocCtrl.NTCValueGatherFlag==1))
		{
			#if (NTCSignalENABLE==1)
			{			
				ClrBit(P1_OE, P12); 
				ClrBit(P1_OE, P16);
				ClrBit(P2_OE, P21);
				
				SetBit(ADC_MASK, CH8EN);   
				SetBit(P2_AN, PIN1);
			}	
			#endif			
		}
		else if ((LEDCtl.LEDCnt <= (LED_Time*6+5))&&(mcFocCtrl.NTCValueGatherFlag==1))
		{ 
																											//延迟5个载波时间，待NTC信号稳定再采集NTC信号
		}			
		else if ((LEDCtl.LEDCnt <= (LED_Time*6+15))&&(mcFocCtrl.NTCValueGatherFlag==1))
		{ 
			#if (NTCSignalENABLE==1)
			{			
				mcFocCtrl.NTCValue = ADC8_DR;    /* -----采集NTC温度----- */
			}	
			#endif			
		}  
		else
		{	
			#if (NTCSignalENABLE==1)
			{			
				mcFocCtrl.NTCValueGatherFlag =0;
				ClrBit(ADC_MASK, CH8EN);   
				ClrBit(P2_AN, PIN1);
			}	
			#endif		
			LEDCtl.LEDCnt = 0;
		}	

		
	
}


