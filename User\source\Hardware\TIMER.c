/* --------------------------- (C) COPYRIGHT 2021 Fortiortech ShenZhen -----------------------------
	File Name      : TIMER.c
	Author         : Fortiortech  Appliction Team
	Version        : V1.0
	Date           : 2021-04-11
	Description    : This file contains .C file function used for Motor Control.
----------------------------------------------------------------------------------------------------
									   All Rights Reserved
------------------------------------------------------------------------------------------------- */
#include <MyProject.h>

#include <TIMER.h>

void Timer1_Init(void)
{
}

void Timer2_Init(void)
{
	ClrBit(TIM2_CR1, T2CEN); // TIM2使能	0-->Disable  1-->Enable

	ClrBit(PH_SEL, T2SEL);	// P10复用作为 Timer2 的输入输出
	ClrBit(PH_SEL, T2SSEL); // P07复用作为 Timer2 的输入输出

	ClrBit(TIM2_CR0, T2PSC2); // 计数器时钟分频选择
	SetBit(TIM2_CR0, T2PSC1); // 000-->24M		001-->12M		010-->6M	011-->3M
	SetBit(TIM2_CR0, T2PSC0); // 100-->1.5M	101-->750K		110-->375K	111-->187.5K

	ClrBit(TIM2_CR0, T2MOD1); // 00-->输入Timer模式  			01-->输出模式
	SetBit(TIM2_CR0, T2MOD0); // 10-->输入Counter模式  		11-->QEP&ISD&步进模式

	ClrBit(TIM2_CR0, T2OCM); // 0: TIM2__CNTR < TIM2__DR，输出 0;TIM2__CNTR ≥ TIM2__DR，输出 1
	// 1: TIM2__CNTR < TIM2__DR，输出 1;TIM2__CNTR ≥ TIM2__DR，输出 0
	ClrBit(TIM2_CR0, T2CES); // 清零脉冲计数器不使能
	SetBit(TIM2_CR0, T2IRE); // 比较匹配中断/脉宽检测中断0-->Disable  1-->Enable
	SetBit(TIM2_CR1, T2IFE); // 计数器上溢中断使能 0-->Disable  1-->Enable

	ClrBit(TIM2_CR1, T2IR | T2IF | T2IP); // 清零中断标志位
	ClrBit(TIM2_CR1, T2IPE);			  // 输入Timer PWM周期检测中断使能 0-->Disable  1-->Enable

	ClrBit(TIM2_CR1, T2FE);	 // 输入噪声滤波使能，小于4个时钟周期脉宽滤除
	ClrBit(TIM2_CR1, T2DIR); // QEP&ISD&步进模式专用：当前的方向 0-->正向	1-->反向

	SetBit(IP1, PTIM21);
	SetBit(IP1, PTIM20); // 中断优先级别为2

	TIM2__ARR = 30000; // //3M分频，1/3us,30000对应延迟10ms
	TIM2__DR = 0;
	TIM2__CNTR = 0;

	SetBit(TIM2_CR1, T2CEN); // TIM2使能	0-->Disable  1-->Enable
}

/* -------------------------------------------------------------------------------------------------
	Function Name  : Timer3_Init
	Description    : 定时器3初始化
	Date           : 2021-11-08
	Parameter      : None
------------------------------------------------------------------------------------------------- */
void Timer3_Init(void)
{
	SetBit(PH_SEL, T3SEL); // Timer3端口使能

	ClrBit(PH_SEL1, T3CT0);
	SetBit(PH_SEL1, T3CT1); //  00:P11   X1:P01  10:P47

	SetBit(TIM3_CR0, T3PSC2); // 计数器时钟分频选择
	SetBit(TIM3_CR0, T3PSC1); // 000-->24M		001-->12M		010-->6M	011-->3M
	ClrBit(TIM3_CR0, T3PSC0); // 100-->1.5M	101-->750K		110-->375K	111-->187.5K

	ClrBit(TIM3_CR0, T3OCM);

	ClrBit(TIM3_CR0, T3IRE); // 比较匹配中断/脉宽检测中断0-->Disable  1-->Enable
	ClrBit(TIM3_CR0, T3OPM); // 0-->计数器不停止		1-->单次模式

	SetBit(TIM3_CR1, T3IPE); // 输入Timer PWM周期检测中断使能 0-->Disable  1-->Enable
	SetBit(TIM3_CR1, T3IFE); // 计数器上溢中断使能 0-->Disable  1-->Enable

	ClrBit(TIM3_CR1, T3NM1); // 输入噪声脉宽选择
	ClrBit(TIM3_CR1, T3NM0); // 00-->不滤波	01-->4cycles    10-->8cycles  11-->16cycles

	//    /* 定时器配置  */
	//  SetBit(IP2, PTIM31);    //1ms定时中断优先级别为1
	//  SetBit(IP2, PTIM30);
	//	TIM3__DR = 1200;
	//	TIM3__ARR = 2400;
	ClrBit(TIM3_CR0, T3MOD); // 0-->Timer模式       1-->输出模式
	SetBit(TIM3_CR1, T3EN);	 // TIM3使能    0-->Disable  1-->Enable
}
// void Timer4_Init(void)
//{
//     ClrBit(PH_SEL , T4SEL);     //Timer4端口使能
//     ClrBit(PH_SEL1 , T4CT);     //默认端口为P11,功能转移后为P01,需TIMER4转移到P00

//    ClrBit(TIM4_CR0 , T4PSC2);	//计数器时钟分频选择
//    ClrBit(TIM4_CR0 , T4PSC1);	//000-->24M		001-->12M		010-->6M	011-->3M
//    ClrBit(TIM4_CR0 , T4PSC0);	//100-->1.5M	101-->750K		110-->375K	111-->187.5K

//    ClrBit(TIM4_CR0 , T4OCM);
//    ClrBit(TIM4_CR0 , T4IRE);	//比较匹配中断/脉宽检测中断0-->Disable  1-->Enable
//    ClrBit(TIM4_CR0 , T4OPM);	//0-->计数器不停止		1-->单次模式

//    ClrBit(TIM4_CR1 , T4IPE);	//输入Timer PWM周期检测中断使能 0-->Disable  1-->Enable
//    ClrBit(TIM4_CR1 , T4IFE);	//计数器上溢中断使能 0-->Disable  1-->Enable
//    ClrBit(TIM4_CR1 , T4NM1);	//输入噪声脉宽选择
//    ClrBit(TIM4_CR1 , T4NM0);	//00-->不滤波	01-->4cycles    10-->8cycles  11-->16cycles

//    TIM4__ARR = 4800;
//    TIM4__DR = TIM4__ARR>>1 ;
//    TIM4__CNTR = 1;

//    PTIM41 = 1;
//    PTIM40 = 1;
//    SetBit(TIM4_CR0 , T4MOD);   //0-->Timer模式       1-->输出模式
//    ClrBit(TIM4_CR1 , T4EN);    //TIM3使能    0-->Disable  1-->Enable
//}
void TIM4_Init_RF(void)
{
}
