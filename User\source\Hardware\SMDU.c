/*  --------------------------- (C) COPYRIGHT 2021 Fortiortech ShenZhen -----------------------------
    File Name      : main.c
    Author         : Fortiortech  Appliction Team
    Version        : V1.0
    Edit           : Leo.li
    Date           : 2021-11-06
    Description    : This file contains XX-XX-XX function used for Motor Control.
    ----------------------------------------------------------------------------------------------------
                                       All Rights Reserved
    ------------------------------------------------------------------------------------------------- */
/********************************************************************************
    Header Definition
********************************************************************************/
#include <MyProject.h>

/* ----------------------------------------------------------------------------------------------------------------------------
                                            PI调用                                                  
---------------------------------------------------------------------------------------------------------------------------- */
/*  -------------------------------------------------------------------------------------------------
    Function Name  : HW_One_PI
    Description    : PI
    Date           : 2021-08-08
    Parameter      : Xn1: [输入]
    ------------------------------------------------------------------------------------------------- */
int16 HW_One_PI(int16 Xn1)
{
    PI1_EK =  Xn1;                                                                               //填入EK
    SMDU_RunBlock(1, PI);
    return PI1_UKH;
}
/*  -------------------------------------------------------------------------------------------------
    Function Name : int16 HW_One_PI2(int16 Xn1, int16 Yn0, int16 Xn2)
    Description   : PI控制
    Input         : Xn1--E(K)
    Output        : PI_UK--当前PI输出值,执行时间us
    -------------------------------------------------------------------------------------------------*/
int16 HW_One_PI2(int16 Xn1)
{
    PI2_EK =  Xn1;                                                                               //填入EK
    SMDU_RunBlock(2, PI);
    return PI2_UKH;
}
/*  -------------------------------------------------------------------------------------------------
    Function Name : int16 HW_One_PI3(int16 Xn1, int16 Yn0, int16 Xn2)
    Description   : PI控制
    Input         : Xn1--E(K)
    Output        : PI_UK--当前PI输出值,执行时间us
    -------------------------------------------------------------------------------------------------*/
int16 HW_One_PI3(int16 Xn1)
{
    PI3_EK =  Xn1;                                                                               //填入EK
    SMDU_RunBlock(3, PI);
    return PI3_UKH;
}

/* ----------------------------------------------------------------------------------------------------------------------------
                                            LPF调用                                                  
---------------------------------------------------------------------------------------------------------------------------- */

/*  -------------------------------------------------------------------------------------------------
    Function Name  : LPFFunction
    Description    : 低通滤波函数
    Date           : 2021-08-08
    Parameter      : Xn1: [输入]
**                   Xn0: [输入]
**                   K: [输入]
    ------------------------------------------------------------------------------------------------- */
int16 LPFFunction(int16 Xn1, int16 Xn0, int8 K)
{
    LPF2_K = K << 8;
    LPF2_X = Xn1;
    LPF2_YH = Xn0;
    SMDU_RunBlock(2, LPF);
    return LPF2_YH;
}


/* ----------------------------------------------------------------------------------------------------------------------------
                                            Atan调用                                                  
---------------------------------------------------------------------------------------------------------------------------- */

/*  -------------------------------------------------------------------------------------------------
    Function Name  : Atan_Us_MDU
    Description    : XX-XX-XX
    Date           : 2021-11-06
    Parameter      : Xn1: [输入/出]
**                 Xn0: [输入/出]
**                 K: [输入/出]
    ------------------------------------------------------------------------------------------------- */
int16 Atan_Us_MDU(int16 Xn1, int16 Xn0)
{
    SCAT1_COS = Xn1;
    SCAT1_SIN = Xn0;
    SCAT1_THE = 0;
    SMDU_RunBlock(1, ATAN);
    return SCAT1_RES1;
}


/* ----------------------------------------------------------------------------------------------------------------------------
                                            除法调用                                                  
---------------------------------------------------------------------------------------------------------------------------- */
/*  -------------------------------------------------------------------------------------------------
    Function Name  : Atan_Us_MDU
    Description    : XX-XX-XX
    Date           : 2021-11-06
    Parameter      : Xn1: [输入/出]
**                       Xn0: [输入/出]
**                       Yn:  [输入/出]
    ------------------------------------------------------------------------------------------------- */
int16 DivQ_L_MDU(uint16 Xn1, uint16 Xn0, uint16 Yn)
{
    DIV0_DAH = Xn1;
    DIV0_DAL = Xn0;
    DIV0_DB  = Yn;
    SMDU_RunBlock(0, DIV);
    return DIV0_DQL;
}

/* ----------------------------------------------------------------------------------------------------------------------------
                                            Sin-Cos调用                                                  
---------------------------------------------------------------------------------------------------------------------------- */
/** 
 * @brief       计算Is
 * @param[in]   i_alp: alp轴电流
 * @param[in]   i_bet: bet轴电流
 * @return      is
 * @date        2022-07-29
 */
uint16 Sqrt_alpbet(int16 i_alp, int16 i_bet)
{
    SCAT2_COS    =   i_alp;
    SCAT2_SIN    =   i_bet;
    SMDU_RunBlock(2, ATAN);
    return SCAT2_RES1;
}

