/*
 * @Description: 
 * @Version: 1.0
 * @Author: <PERSON><PERSON>
 * @Date: 2023-11-29 22:11:54
 * @LastEditors: <PERSON>sen
 * @LastEditTime: 2023-12-12 20:39:50
 */
/**************************** (C) COPYRIGHT 2015 Fortiortech shenzhen *****************************
 * File Name          : KeyScan.h
 * Author             : <PERSON>iortech  Market Dept
 * Version            : V1.0
 * Date               : 01/07/2015
 * Description        : This file contains all the common data types used for Motor Control.
 ***************************************************************************************************
 * All Rights Reserved
 **************************************************************************************************/

/* Define to prevent recursive inclusion --------------------------------------------------------*/
#ifndef __KEYSCAN_H_
#define __KEYSCAN_H_

#define SW3 GP33 
#define SW1 GP33 
#define SW2 GP26 

/* Exported types -------------------------------------------------------------------------------*/
typedef struct
{
	uint8 Key1Value;
	uint8 Key2Value;
	uint8 Key3Value;

	uint16 Key1PressCnt;
	uint16 Key2PressCnt;
	uint16 Key2PressCnt2;
	uint16 Key3PressCnt;
	uint16 Key3releaseCnt;

	uint16 KeyADCValue;

	uint8 KeyValuetotal;
	uint8 OldKeyValuetotal;
	uint8 ChangeKeyFlg;
	uint8 TwoCarrierCnt;

	uint8 ColdHotFlag;
	uint16 ONOFFCNT;
	uint8 ONOFFFlag;

} KeyScanParam_TypeDef;

/* Exported variables ---------------------------------------------------------------------------*/
extern KeyScanParam_TypeDef xdata KS;

/* Exported functions ---------------------------------------------------------------------------*/
extern void KeyInit(void);
extern int KeyValue(void);
extern int KeyValue1(void);
extern void KeyScan(void);

#endif
