[USBTOOL]
DEV_NUM=16
;Is enable log the downlaod message. 0 = Disable, 1 = Enable
ENABLE_LOG=1
;The default scrip file for download step.
BOOT_SCRIPT=Fice.conf
;The default scrip file for update step (Only use in update tool).
UpdateConf=gpl162002update.conf
;A download description string in update dialog (Only use in update tool).
DownloadDescription=download description
;Set Disk Type: 0 = No Disk, 1 = Disk
DiskType=0
;Set USBTable Type: 0 = Mass Production, 1 = Real Table
TableType=1
;Set Authorize Type: 0 = Standard, 1 = Easy
AuthorizeType=1
;Set time-out when execute IO task
Timeout_Value=20
;download data length each time
DataLength=4KB|8KB|16KB|32KB|64KB|
CodePackerPath=
VID=GENPLUS
PID=USB-MSDC_DISK_A
ENBLE_SETTINGBTN=1

[DeviceInitCommand]
; A command that write to device when this device plugin.
ID=

[162002_MCS]
MCS0=1F84
MCS1=4
MCS2=C5
MCS3=4
MCS4=4
ENABLE=0

[162004_MCS]
MCS0=1F89
MCS1=FF4F
MCS2=C3
MCS3=3F
MCS4=3F
ENABLE=1

[ChipType]
0=SLC Nand < 128MB(Physical, Small Page)
1=SLC Nand = 128MB(Physical, Small Page)
2=SLC Nand > 128MB(Physical, 2K Page)
3=RAM
4=SLC & MLC Data Area
5=Nor Flash
6=SPI Nor Flash
0x80=MLC 2K Page(Physical)
0x81=MLC 4K Page(Physical)
0x82=SD Card
0x83=MLC & SLC APP Area
0x84=MLC 2k + 64byte(Physical)
0x23=Nand OTP
0x24=AutoWriteBootArea
0x25=Register

[ActionDescription]
ACT_READ=Read
ACT_WRITE=Write
ACT_ERASE=Erase
ACT_JUMP=Jump to Function
ACT_INITNAND=Initial Nand Flash
ACT_RESETDEVICE=Reset Device
ACT_SETNONMOUNT=Set Non Mount
ACT_SENDCOMMAND=Send Command
ACT_LOWLEVELFORMAT=Low Level Format
ACT_CHIPERASE=Chip Erase
ACT_APPINIT=App Initial
;ACT_COMPAREFILE=CompareFile
;replan Send command 0xff40, 0xff29
ACT_SETAPPVARIABLE=Set App Variable
ACT_NANDFLASH=Write Nand Flush
ACT_ERASEALLBLK=Erase All Block
ACT_WRITESERIALNUMBER=Write Serial Number
ACT_PROBE_FIRMWARE=ProbePath
ACT_BACKUPSERIALNUMBER=Backup Serial Number
ACT_DELAYCOMMAND=DelayStep
ACT_WRITEROMCODE=Write To RomCode

[NorFlashType]
;Format : Device type = Block size
;The below is manufactory Macronix
MX29LV160T=35
MX29LV160B=35
MX29LV800T=11
MX29LV800B=11
MX29LV320T=71
MX29LV320B=71
;The below is manufactory SST
SST39VF1601=32
SST39VF3201=64
SST39VF6401=128

[FT68xxUIOpt]
LOCK_ENABLE=1
LOCK_BYTE=0
LOCK_FULL=1
LOCK_START_SEC=0
LOCK_END_SEC=2
;LVD Setting
LVD_EN=0
LVW_EN=0
LVR_SEL=3
LVW_SEL=0
;clock setting
FCK_MODE=0
EC_MODE=0
XT_GF_CFG=3
XT_CUR_CFG=0
SPEED2=1
MCD_EN=1
;watch-dog setting
WDT_EN=0
WDT_RST_EN=0
WDT_BT_EN=0
PRE_CHIP=0
VBB_EN=0

[NorFlashManufacturer]
SST=1
AMD/MX=2
Intel=3

[UserSelfDefineInfo]
;icon name
Icon_Name=usbtool.ico
;company name
Company_Name=G+ Mass Production Tool(Patch1)

[ReadbackVerify_Status]
Continue=0

