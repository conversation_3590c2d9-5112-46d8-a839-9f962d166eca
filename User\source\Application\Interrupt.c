/**
	@copyright (C) COPYRIGHT 2022 Fortiortech Shenzhen
	@file      Interrupt.c
	<AUTHOR>  Appliction Team
	@date      2022-07-13
	@brief     This file contains interrupt function used for Motor Control.
*/

#include <MyProject.h>

extern uint8 data g_1mTick; ///< 1ms滴答信号，每隔1ms在SYSTICK定时器被置1，需在大循环使用处清零

uint16 xdata spidebug[4] = {0}; ///< SPI debug 输出通道缓存，SPI调试器会将该变量值进行输出
//uint16 xdata Power_Currt;

/**
	@brief        低于预警中断与过温中断
	@brief        开启低压检测中断后,MCU会对输入电压进行监测,当输入电压低于设定值，则会触发中断
	@brief        开启过温保护中断后,MCU会对内部结温进行监测,当内部结温高于设定值，则会触发中断
	@date         2022-07-14
*/
void LVW_TSD_INT(void) interrupt 0 // LVW & TSD interrupt
{
	if (ReadBit(LVSR, LVWIF))
	{
		if (ReadBit(LVSR, LVWF))
		{
			mcFaultSource = FaultUnderVoltageDC;
			ClrBit(LVSR, LVWF);
		}

		ClrBit(LVSR, LVWIF);
	}

	if (TSDIF)
	{
		if (ReadBit(LVSR, TSDF))
		{
			mcFaultSource = FaultTSD;
			ClrBit(LVSR, TSDF);
		}

		TSDIF = 0;
	}
}

/**
	@brief        外部中断0
	@brief        一般用于响应IPM的FO过流信号
	@date         2022-07-14
*/
void EXTERN0_INT(void) interrupt 1 //  外部中断0
{
	if (IF0)
	{
		IF0 = 0; // clear P00 interrupt flag
	}
}
/**
	@brief        FOC中断(Drv中断),每个载波周期执行一次，用于处理响应较高的程序，中断优先级第二
	@date         2022-07-14
*/
void DRV_ISR(void) interrupt 3
{
	if (ReadBit(DRV_SR, FGIF))
	{
		ClrBit(DRV_SR, FGIF);
	}

	if (ReadBit(DRV_SR, DCIF)) // 比较中断
	{
#if (DBG_MODE == DBG_SPI_SW)   // 软件调试模式
		spidebug[0] = mcFocCtrl.SPIVar3; // mcFocCtrl.mcDcbusFlt;
		spidebug[1] = FOC__UQ;
//		spidebug[2] = FOC__THETA; // mcBemf.BEMFSpeed;
//		spidebug[3] = FOC__THETA;
#endif

#if (VoltageCompensationEn == 1)
		{
			if (VoltageComp.cpscnt > VoltageCompensationDelayCnt)
			{
				SetBit(ADC_CR, ADCBSY);
				while (ReadBit(ADC_CR, ADCBSY));
				mcFocCtrl.mcDcbusFlt = ADC2_DR;				
			}			
			VoltageCompensation(mcFocCtrl.CtrlMode,mcFocCtrl.mcDcbusFlt);
		}
#endif

#if (SPEED_MODE == KEYSCANMODE)  //SPEED_MODE == KEYSCANMODE
		{
				uint8 uLedMask = 0;

				if (isCtrlPowOn && (mcState != mcFault))
				{
					uint8 b7 = KS.KeyValuetotal & 0x70;
					uLedMask |= LED1_MASK;//LED1 = 1;
					if (b7 == 0x10) {}
					else if (b7 == 0x20) uLedMask |= LED2_MASK;//LED2 = 1;
					else if (b7 == 0x40) uLedMask |= LED2_MASK | LED3_MASK;//LED3 = 1;
					else goto _end_display;
					
					if (!(KS.KeyValuetotal & 0x80)) // SW3 没有按下
					{
						uint8 bF = KS.KeyValuetotal & 0x0F;
						
						if (bF == 0x01) {}
						else if (bF == 0x02) uLedMask |= LED4_MASK;//LED4 = 1;
						else if (bF == 0x04) uLedMask |= LED4_MASK | LED5_MASK;//LED4 |= LED5 = 1;
						else if (bF == 0x08) uLedMask |= LED4_MASK | LED5_MASK | LED6_MASK;//LED4 = LED5 = LED6 = 1;
						else goto _end_display;
					}
					else
					{
						if (b7 == 0x40) uLedMask |= LED2_MASK;//LED2 = 1;
					}
				}
				LedDisplay(uLedMask);
				_end_display :;

		}
#endif

		DRV_SR = (DRV_SR | SYSTIF) & (~DCIF);
	}
}

/**
	@brief        Timer2中断服务函数
	@note         本例程中用于RSD方式顺逆风检测
	@date         2022-07-14
*/
#if (TAILWIND_MODE == RSDMethod)
void TIM2_INT(void) interrupt 4
{
	if (ReadBit(TIM2_CR1, T2IP))
	{
		RsdProcess();
		ClrBit(TIM2_CR1, T2IP);
	}

	if (ReadBit(TIM2_CR1, T2IF)) // 溢出中断,用于判断静止,时间为349ms。
	{
		mcRsd.State = STATIC;
		mcRsd.Period = 65535;
		mcRsd.Speed = 0;
		mcRsd.SpeedUpdate = 1;
		ClrBit(TIM2_CR1, T2IF);
	}

	if (ReadBit(TIM2_CR1, T2IR))
	{
		ClrBit(TIM2_CR1, T2IR);
	}
}

#endif

/**
	@brief        Timer1中断服务函数
	@note         本例程中用于BEMF方式顺逆风检测
	@date         2022-07-14
*/
#if (TAILWIND_MODE == BEMFMethod)
void CMP012_INT(void) interrupt 7
{
	if (CMP_SR & 0x70)
	{
		BemfProcess();
		CMP_SR = CMP_SR & 0x8F;
	}
}
void TIM2_INT(void) interrupt 4
{
	if (ReadBit(TIM2_CR1, T2IP))
	{
		ClrBit(TIM2_CR1, T2IP);
	}

	if (ReadBit(TIM2_CR1, T2IF)) // 溢出中断,用于判断静止,时间为349ms。
	{
		mcBemf.PeriodTime = 65535;
		mcBemf.BEMFSpeed = 0;
		mcBemf.SpeedUpdate = 1;
		ClrBit(TIM2_CR1, T2IF);
	}

	if (ReadBit(TIM2_CR1, T2IR))
	{
		ClrBit(TIM2_CR1, T2IR);
	}
}
#endif


void TIM2_INT(void) interrupt 4
{
	if (ReadBit(TIM2_CR1, T2IP))
	{
		ClrBit(TIM2_CR1, T2IP);
	}

	if (ReadBit(TIM2_CR1, T2IF)) // 溢出中断,用于判断静止,时间为349ms。
	{
		ClrBit(TIM2_CR1, T2IF);
	}

	if (ReadBit(TIM2_CR1, T2IR))
	{
		ClrBit(TIM2_CR1, T2IR);
	}
}


/**
	@brief        定时器3中断服务函数
	@note         本例程中用于PWM调速信号捕获
	@date         2022-07-14
*/
void TIM3_INT(void) interrupt 9
{
	static uint16 ScrOnTime = 0; // 发热开启时间

	if (ReadBit(TIM3_CR1, T3IR))
	{
		ClrBit(TIM3_CR1, T3IR);
	}

	if (ReadBit(TIM3_CR1, T3IP)) // 周期中断
	{
#if (AC_ZeroCrossing_MODE == 1)
		{
			mcFocCtrl.ZeroCnt = 0;
			mcFocCtrl.ZeroFlag = 1;
			mcFocCtrl.ZeroLossCnt = 0;
			mcFocCtrl.ZeroLossFlag = 0;
		}
#endif
	
		
#if (HeatingControlSignalENABLE == 1)
		{
			if (User.ScrOnZeroFlag == 0)
			{
				User.ScrOnZeroFlag = 1;
				if ((User.TPCtrlDealy < 4600) && (mcState == mcRun) && (mcFocCtrl.SpeedFlt > 12000))
				{
					ScrOnTime++;
					if (ScrOnTime <= User.ScrOnPeriod)
					{
						if ((ScrOnTime <= User.ScrOnAngleTimeDatum) && (User.ScrOnAngleTime != Temperature_Off))
						{
							GP01 = 0; // 打开发热丝
						}
						else
						{
							GP01 = 1; // 关闭发热丝
						}
					}
					else
					{
						ScrOnTime = 0;

						Temperature_Control(); // 发热功率控制
					}
				}
				else
				{
					GP01 = 1; // 关闭发热丝
				}
			}
			if (User.ScrOnZeroFlag == 1)
			{
				User.ScrOnZeroFlag = 2;
			}
		}
#endif

		ClrBit(TIM3_CR1, T3IP);
	}

	if (ReadBit(TIM3_CR1, T3IF))
	{
		ClrBit(TIM3_CR1, T3IF);
	}
}

/**
	@brief        滴答定时器，默认用于产生1ms定时间隔
	@date         2022-07-14
*/
void SYStick_INT(void) interrupt 10
{
	if (ReadBit(DRV_SR, SYSTIF)) // SYS TICK中断
	{
		g_1mTick = 1;
		DRV_SR = (DRV_SR | DCIF) & (~SYSTIF);
	}
}

/**
	@brief        比较器硬件过流保护，该中断仅提供 故障码 赋值,用于状态机的切换。
				 需要开启比较器CMP3  发生过流 自动清除MOE功能
	@date         2022-07-14
*/

void CMP3_INT(void) interrupt 12
{
	if (ReadBit(CMP_SR, CMP3IF))
	{
		if (mcState != mcPosiCheck)
		{
			mcFaultSource = FaultHardOVCurrent; // 硬件过流保护
		}

		ClrBit(CMP_SR, CMP3IF);
	}
}
