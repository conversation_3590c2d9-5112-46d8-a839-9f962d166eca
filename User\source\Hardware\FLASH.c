/* --------------------------- (C) COPYRIGHT 2021 Fortiortech ShenZhen -----------------------------
    File Name      : FLASH.c
    Author         : Fortiortech  Appliction Team
    Version        : V1.0
    Date           : 2021-07-24
    Description    : This file contains FLASH function used for Motor Control.
----------------------------------------------------------------------------------------------------  
                                       All Rights Reserved
------------------------------------------------------------------------------------------------- */
/******************************************************************************///Including Header Files


#include <Flash.h>

/* -------------------------------------------------------------------------------------------------
    Function Name  : Flash_Sector_Erase
    Description    : 扇区自擦除: 指定将要擦除的Flash扇区，每个扇区128Byte，共128个扇区，扇区0~127对应Flash地址0x0000~0x3fff，
                     通过指定Flash地址来指定要擦除的Flash地址所在扇区。
                     一次只能擦除一个扇区，自擦除数据为任意值，一定要在解锁后才给DPTR赋值。
    Date           : 2021-07-23
    Parameter      : FlashAddress: [输入/出] 
------------------------------------------------------------------------------------------------- */
void Flash_Sector_Erase(uint8 xdata *FlashAddress)
{
    
        EA = 0;                        //Flash自擦除前先关总中断
        FLA_CR = 0x03;                 //使能自擦除
        FLA_KEY = 0x5a;
        FLA_KEY = 0x1f;                //flash预编程解锁
        
        *FlashAddress = 0xff;          //写任意数据
        FLA_CR = 0x08;                 //开始预编程，完成后Flash再次上锁
   
}


/* -------------------------------------------------------------------------------------------------
    Function Name  : Flash_Sector_Write
    Description    : Flash自烧写: 对扇区预编程和自擦除后，可以对扇区内的地址进行Flash烧写，
                    一次烧写一个byte,一定要在解锁后才给DPTR赋值
    Date           : 2021-07-23
    Parameter      : FlashAddress: [输入/出] 
------------------------------------------------------------------------------------------------- */
void Flash_Sector_Write(uint8 xdata *FlashAddress, uint8 FlashData)
{
        
            EA = 0;                   //Flash自烧写前先关总中断
       
        FLA_CR = 0x01;                // 使能Flash编程
        FLA_KEY = 0x5a;
        FLA_KEY = 0x1f;               // flash预编程解锁
        *FlashAddress =FlashData;     // 写编程数据
        FLA_CR = 0x08;                // 开始预编程，完成后Flash再次上锁
    
 



}

MCUFlash xdata FlashTest;            //flash测试
/* -------------------------------------------------------------------------------------------------
    Function Name  : void WriteData2Flash(uint8 xdata *BlockStartAddr,uint16 NewData2Flash)
    Description    : 从目标FLASH扇区读取2字节最新写入的数据。Input:	uint8 xdata *BlockStartAddr：目标FLASH扇区
	Output:	读出的数据
                    
    Date           : 2021-07-23
    Parameter      : None
------------------------------------------------------------------------------------------------- */
uint16 Get2ByteFromFlash(uint8 xdata *BlockStartAddr)
{
	uint8 xdata *FlashStartAddr = BlockStartAddr;
	uint8 i;
	uint16 tempofFlashData;
	
	for(i=0;i<64;i++)
	{
		tempofFlashData = *(uint16 code *)(FlashStartAddr+2*i);   
		if(tempofFlashData==0)
		{
			if(i!=0)
			{
				tempofFlashData = *(uint16 code *)(FlashStartAddr+2*(i-1));
				return tempofFlashData;
			}
			else
			{
				return 0;
			}
		}
		else
		{
			if(i==63)
			{
				return tempofFlashData;
			}
		}	
	}
	return 1;
}
/* -------------------------------------------------------------------------------------------------
    Function Name  : void WriteData2Flash(uint8 xdata *BlockStartAddr,uint16 NewData2Flash)
    Description    : 写入2个字节到FLASH。其中Input：uint8 xdata *BlockStartAddr：目标FLASH地址  NewData2Flash：被写入数据
    Output：1:扇区未满,写入完成  0:扇区已满,写入失败
                    
    Date           : 2021-07-23
    Parameter      : None
------------------------------------------------------------------------------------------------- */
uint8 Write2Byte2Flash(uint8 xdata *BlockStartAddr,uint16 NewData2Flash)
{
	uint8 xdata *FlashStartAddr = BlockStartAddr;
	uint16 tempofFlashData=0;
	uint16 tempofNewFlashData=0;
	uint8 i;
	
	tempofNewFlashData = NewData2Flash;
	
	for(i=0;i<64;i++)
	{
		tempofFlashData = *(uint16 code *)(FlashStartAddr+2*i);
		if(tempofFlashData==0)
		{
			tempofFlashData = tempofNewFlashData>>8;                            //待写入数据首字节
			Flash_Sector_Write((FlashStartAddr+2*i),(uint8)tempofFlashData);
		
			tempofFlashData = tempofNewFlashData&0x00ff;                       //待写入数据末字节   
			Flash_Sector_Write((FlashStartAddr+2*i+1),(uint8)tempofFlashData);
		 return 1;
		}
		else
		{
			if(i==63)
			{
				return 0;
			}
		}
	}
	return 0;
}
/* -------------------------------------------------------------------------------------------------
    Function Name  : void WriteOrReadFlashTest(uint8 xdata *BlockStartAddr,uint16 NewData2Flash)
    Description    :FLASH读写数据测试,先把NewData2Flash写入到目标FLASH地址BlockStartAddr后,再将其FLASH值读出来。
	uint8 xdata *BlockStartAddr：目标FLASH地址  NewData2Flash：被写入数据
                    
    Date           : 2021-07-23
    Parameter      : None
------------------------------------------------------------------------------------------------- */
uint8 FlashWriteStatus=0;
void WriteOrReadFlashTest(uint8 xdata *BlockStartAddr,uint16 NewData2Flash)
{
  
        
//	if(FlashTest.FlashWrite)
//	{
//        FlashWriteStatus = Write2Byte2Flash(BlockStartAddr,NewData2Flash);
//        
//        if(!FlashWriteStatus)
//        {
//            Flash_Sector_Erase(BlockStartAddr);	
//            FlashWriteStatus = Write2Byte2Flash(BlockStartAddr,NewData2Flash);				
//        }			
//		
//        FlashTest.FlashWrite = 0;
        FlashTest.FlashRead  = 1;
//	}
    
   if(FlashTest.FlashRead)
	{	
		FlashTest.FlashRead2Byte = Get2ByteFromFlash(BlockStartAddr);
		
        FlashTest.FlashRead = 0;
	}


}
