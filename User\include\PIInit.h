/**************************** (C) COPYRIGHT 2015 Fortiortech shenzhen *****************************
* File Name          : PIInit.h
* Author             : Fortiortech  Market Dept
* Version            : V1.0
* Date               : 01/07/2015
* Description        : This file contains all the common data types used for Motor Control.
***************************************************************************************************
* All Rights Reserved
**************************************************************************************************/ 

/* Define to prevent recursive inclusion --------------------------------------------------------*/
#ifndef _PIINIT_H_
#define _PIINIT_H_

/* Exported functions ---------------------------------------------------------------------------*/
extern void PI_Init(void);
extern void PI2_Init(void);
extern void PI3_Init(void);

#endif