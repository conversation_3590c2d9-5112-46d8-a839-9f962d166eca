/*  -------------------------- (C) COPYRIGHT 2022 Fortiortech ShenZhen ---------------------------*/
/**
 * @copyright (C) COPYRIGHT 2022 Fortiortech Shenzhen
 * @file      LED.c
 * <AUTHOR>  Appliction Team
 * @since     Create:2022-07-14
 * @date      Last modify:2022-07-14
 * @note      Last modify author is <PERSON>.li
 * @brief      
 */

/* Define to prevent recursive inclusion --------------------------------------------------------*/
#ifndef __LED_H_
#define __LED_H_
//#define PID_CONTROL_TEM

/* Exported types -------------------------------------------------------------------------------*/
typedef struct
{
	uint16 LEDCnt;
	uint8 FlagLED;
//	uint8 FlagStepTime;
//	uint16 LEDTimeCnt;	
} LEDCtl_TypeDef;


//#define D1              GP13
//#define D2              GP14
//#define D3              GP33
//#define D4              GP07
//#define D5              GP06
//#define D6              GP05

/* Exported variables ---------------------------------------------------------------------------*/
extern LEDCtl_TypeDef LEDCtl;

#define LED1_MASK	((uint8)(1 << 0))
#define LED2_MASK	((uint8)(1 << 1))
#define LED3_MASK	((uint8)(1 << 2))
#define LED4_MASK	((uint8)(1 << 3))
#define LED5_MASK	((uint8)(1 << 4))
#define LED6_MASK	((uint8)(1 << 5))

/* Exported functions ---------------------------------------------------------------------------*/
extern void LEDControl(void);
extern void Temperature_Control(void);
extern void LedDisplay(uint8 uLedMask);//uint8 LED1, uint8 LED2, uint8 LED3, uint8 LED4, uint8 LED5, uint8 LED6);

#endif
