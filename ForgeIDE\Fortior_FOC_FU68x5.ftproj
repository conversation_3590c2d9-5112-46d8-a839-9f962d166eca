<?xml version="1.0" encoding="ISO-8859-2"?>
<FortiortechProject>
	<Properties>
		BodyName="FU6522"
		CLSID="{0D4D5604-6A52-4042-9F34-64D371B53EA6}"
		DebugInfoOnDeviceOpened="1"
		ProjectSettingChanged="0"
		ActiveCfgName="Debug"
		GUID="{A5245B62-3438-44e1-B919-7F50790173FD}"
		Version="1.0.0.0"
		Name="Fortior_FOC_FU68x5"
		BodyPath=".\\Body\\mcu8\\FU6522"
		DowloadMode="0"
		LanguageName="C51"
		BodySeries="MCU"
		DownloadMark=""
		TargArgs="-S1 -B115200 -O1799"
	</Properties>
	<Configurations>
		<Configuration>
			Name="Debug"
			OutputDirectory="./Debug"
			IntermediateDirectory="./Obj"
			OutputFileName="./Debug/Fortior_FOC_FU68x5.hex"
			ExcludedFromBuild="FALSE"
			MakefileOption="0"
			ExternalMakefile=""
			OptionsFile=".\\Options\\SettingsOptions_mcu8.xml"
			<DebuggerProperties>
				MemoryCache="2"
				MemoryCacheBound="512"
				StopAt="1"
				MemoryFeatureCLSID="{B9F22F90-354A-42a6-B2C2-7D4C9E774FBE}"
			</DebuggerProperties>
			<Option>
				<Target>
					Options="-mmcs51 --debug --nostdinc"
				</Target>
				<Assembler>
					Options="-D \"__MCUIDE_FLASH_SPILT__\""
				</Assembler>
				<Compiler>
					<General>
						Options="--std-ftcc11 -I \"$(ProjectPath)\\..\\User\\Include\" -I \"$(ProjectPath)\\..\\FU65xx_Hardware_Driver\\Include\" --disable-all-warning 2 --fsigned-char"
					</General>
					<CodeGeneration>
						Options="--model-large --int-long-reent --float-reent"
					</CodeGeneration>
					<Optimization>
						Options="--opt-3 --peep-asm --peep-return"
					</Optimization>
				</Compiler>
				<Linker>
					Options="-L\"$(ProjectPath)\\..\\User\\source\\Function\" -l\"$(ProjectPath)\\..\\User\\source\\Function\\libVoltageCompensation2.lib\" --xram-size 0x1000 --iram-size 0x100 --code-size 0x8000"
				</Linker>
				<PreLink>
					Descriptions=""
				</PreLink>
				<PostBuild>
					Descriptions=""
					<Commands>
					</Commands>
				</PostBuild>
				<RegisterConfig>
					CheckNofixedCodeArea="FALSE"
				</RegisterConfig>
			</Option>
			<FileCPP>
				PreDefine="__cpluscplus"
				IncludePath=".\\"
			</FileCPP>
			UseEmulation="0"
		</Configuration>
		<Configuration>
			Name="Release"
			OutputDirectory="./Release"
			IntermediateDirectory="./Obj"
			OutputFileName="./Release/Fortior_FOC_FU68x5.hex"
			ExcludedFromBuild="FALSE"
			MakefileOption="0"
			ExternalMakefile=""
			OptionsFile=".\\Options\\SettingsOptions_mcu8.xml"
			<DebuggerProperties>
				MemoryCache="2"
				MemoryCacheBound="512"
				StopAt="1"
				MemoryFeatureCLSID="{B9F22F90-354A-42a6-B2C2-7D4C9E774FBE}"
			</DebuggerProperties>
			<Option>
				<Target>
					Options="-mmcs51 --debug --nostdinc"
				</Target>
				<Assembler>
					Options="-D \"__MCUIDE_FLASH_SPILT__\""
				</Assembler>
				<Compiler>
					<General>
						Options="--std-ftcc11 -I \"$(ProjectPath)\\..\\User\\Include\" -I \"$(ProjectPath)\\..\\FU68xx_Hardware_Driver\\Include\" --fsigned-char"
					</General>
					<CodeGeneration>
						Options="--model-large --int-long-reent --float-reent"
					</CodeGeneration>
					<Optimization>
						Options="--opt-4 --peep-asm --peep-return"
					</Optimization>
				</Compiler>
				<Linker>
					Options="--code-size 0x8000 --iram-size 0x100 --xram-size 0x1000"
				</Linker>
				<PreLink>
					Descriptions=""
				</PreLink>
				<PostBuild>
					Descriptions=""
					<Commands>
					</Commands>
				</PostBuild>
			</Option>
			<FileCPP>
				PreDefine="__cpluscplus"
				IncludePath=".\\"
			</FileCPP>
		</Configuration>
	</Configurations>
	<FilesTab>
		<Folder>
			<Properties>
			</Properties>
			Name="Application"
			Filter=""
			<File>
				FilePath="..\\User\\source\\Application\\main.c"
				Flags="0x000004CF"
				<Properties>
				</Properties>
			</File>
			<File>
				FilePath="..\\User\\source\\Application\\AddFunction.c"
				Flags="0x000004CF"
				<Properties>
				</Properties>
			</File>
			<File>
				FilePath="..\\User\\source\\Application\\Interrupt.c"
				Flags="0x000004CF"
				<Properties>
				</Properties>
			</File>
			<File>
				FilePath="..\\User\\include\\Customer.h"
				<Properties>
				</Properties>
			</File>
			<File>
				FilePath="..\\User\\include\\Parameter.h"
				<Properties>
				</Properties>
			</File>
			<File>
				FilePath="..\\User\\include\\Protect.h"
				<Properties>
				</Properties>
			</File>
		</Folder>
		<Folder>
			<Properties>
			</Properties>
			Name="Function"
			Filter=""
			<File>
				FilePath="..\\User\\source\\Function\\MotorControl.c"
				Flags="0x000004CF"
				<Properties>
				</Properties>
			</File>
			<File>
				FilePath="..\\User\\source\\Function\\MotorControlFunction.c"
				Flags="0x000004CF"
				<Properties>
				</Properties>
			</File>
			<File>
				FilePath="..\\User\\source\\Function\\MotorProtect.c"
				Flags="0x000004CF"
				<Properties>
				</Properties>
			</File>
			<File>
				FilePath="..\\User\\source\\Function\\RSDDetect.c"
				Flags="0x000004CF"
				<Properties>
				</Properties>
			</File>
			<File>
				FilePath="..\\User\\source\\Function\\BEMFDetect.c"
				Flags="0x000004CF"
				<Properties>
				</Properties>
			</File>
			<File>
				FilePath="..\\User\\source\\Function\\FOCTailDect.c"
				Flags="0x000004CF"
				<Properties>
				</Properties>
			</File>
			<File>
				FilePath="..\\User\\source\\Function\\KeyScan.c"
				Flags="0x000004CF"
				<Properties>
				</Properties>
			</File>
			<File>
				FilePath="..\\User\\source\\Function\\LED.c"
				Flags="0x000004CF"
				<Properties>
				</Properties>
			</File>
			<File>
				FilePath="..\\..\\6562T小电容-33uf源程序两对极-20230226\\User\\source\\Function\\libVoltageCompensation2.lib"
				<Properties>
				</Properties>
			</File>
			<File>
				FilePath="..\\User\\source\\Function\\mcFiledWeaken.c"
				Flags="0x000004CF"
				<Properties>
				</Properties>
			</File>
		</Folder>
		<Folder>
			<Properties>
			</Properties>
			Name="HardWare"
			Filter=""
			<File>
				FilePath="..\\User\\source\\Hardware\\ADC.c"
				Flags="0x000004CF"
				<Properties>
				</Properties>
			</File>
			<File>
				FilePath="..\\User\\source\\Hardware\\AMP.c"
				Flags="0x000004CF"
				<Properties>
				</Properties>
			</File>
			<File>
				FilePath="..\\User\\source\\Hardware\\CMP.c"
				Flags="0x000004CF"
				<Properties>
				</Properties>
			</File>
			<File>
				FilePath="..\\User\\source\\Hardware\\CRC.c"
				Flags="0x000004CF"
				<Properties>
				</Properties>
			</File>
			<File>
				FilePath="..\\User\\source\\Hardware\\DMA.c"
				Flags="0x000004CF"
				<Properties>
				</Properties>
			</File>
			<File>
				FilePath="..\\User\\source\\Hardware\\DRIVER.c"
				Flags="0x000004CF"
				<Properties>
				</Properties>
			</File>
			<File>
				FilePath="..\\User\\source\\Hardware\\GPIO.c"
				Flags="0x000004CF"
				<Properties>
				</Properties>
			</File>
			<File>
				FilePath="..\\User\\source\\Hardware\\I2C.c"
				Flags="0x000004CF"
				<Properties>
				</Properties>
			</File>
			<File>
				FilePath="..\\User\\source\\Hardware\\PIInit.c"
				Flags="0x000004CF"
				<Properties>
				</Properties>
			</File>
			<File>
				FilePath="..\\User\\source\\Hardware\\SPI.c"
				Flags="0x000004CF"
				<Properties>
				</Properties>
			</File>
			<File>
				FilePath="..\\User\\source\\Hardware\\TIMER.c"
				Flags="0x000004CF"
				<Properties>
				</Properties>
			</File>
			<File>
				FilePath="..\\User\\source\\Hardware\\UART.c"
				Flags="0x000004CF"
				<Properties>
				</Properties>
			</File>
			<File>
				FilePath="..\\User\\source\\Hardware\\TSD.c"
				Flags="0x000004CF"
				<Properties>
				</Properties>
			</File>
			<File>
				FilePath="..\\User\\source\\Hardware\\SMDU.c"
				Flags="0x000004CF"
				<Properties>
				</Properties>
			</File>
			<File>
				FilePath="..\\User\\source\\Hardware\\FU68xx_5_Flash.c"
				Flags="0x000004CF"
				<Properties>
				</Properties>
			</File>
			<File>
				FilePath="..\\User\\source\\Hardware\\EXTI.c"
				Flags="0x000004CF"
				<Properties>
				</Properties>
			</File>
		</Folder>
		<Folder>
			<Properties>
			</Properties>
			Name="FU65XX_Hardware_Driver"
			Filter=""
			<File>
				FilePath="..\\FU68xx_Hardware_Driver\\Source\\STARTUP_FU6805.A51"
				<Properties>
				</Properties>
			</File>
		</Folder>
		<Folder>
			<Properties>
			</Properties>
			Name="Document"
			Filter=""
			<File>
				FilePath="..\\..\\README.md"
				<Properties>
				</Properties>
			</File>
		</Folder>
		<Folder>
			<Properties>
			</Properties>
			Name="Include"
			Filter=""
			<File>
				FilePath="..\\FU68xx_Hardware_Driver\\Include\\FU68xx_5.h"
				<Properties>
				</Properties>
			</File>
			<File>
				FilePath="..\\FU68xx_Hardware_Driver\\Include\\FU68xx_5_DMA.h"
				<Properties>
				</Properties>
			</File>
			<File>
				FilePath="..\\FU68xx_Hardware_Driver\\Include\\FU68xx_5_MCU.h"
				<Properties>
				</Properties>
			</File>
			<File>
				FilePath="..\\FU68xx_Hardware_Driver\\Include\\FU68xx_5_Type.h"
				<Properties>
				</Properties>
			</File>
			<File>
				FilePath="..\\User\\include\\ADC.H"
				<Properties>
				</Properties>
			</File>
			<File>
				FilePath="..\\User\\include\\ADDFUNCTION.H"
				<Properties>
				</Properties>
			</File>
			<File>
				FilePath="..\\User\\include\\Amp.H"
				<Properties>
				</Properties>
			</File>
			<File>
				FilePath="..\\User\\include\\BEMFDetect.h"
				<Properties>
				</Properties>
			</File>
			<File>
				FilePath="..\\User\\include\\BuzzerScan.h"
				<Properties>
				</Properties>
			</File>
			<File>
				FilePath="..\\User\\include\\Cmp.H"
				<Properties>
				</Properties>
			</File>
			<File>
				FilePath="..\\User\\include\\CRC.H"
				<Properties>
				</Properties>
			</File>
			<File>
				FilePath="..\\User\\include\\Customer_Debug.h"
				<Properties>
				</Properties>
			</File>
			<File>
				FilePath="..\\User\\include\\DAC.H"
				<Properties>
				</Properties>
			</File>
			<File>
				FilePath="..\\User\\include\\definition.h"
				<Properties>
				</Properties>
			</File>
			<File>
				FilePath="..\\User\\include\\DMA.H"
				<Properties>
				</Properties>
			</File>
			<File>
				FilePath="..\\User\\include\\Driver.H"
				<Properties>
				</Properties>
			</File>
			<File>
				FilePath="..\\User\\include\\EXTIInit.h"
				<Properties>
				</Properties>
			</File>
			<File>
				FilePath="..\\User\\include\\FLASH.H"
				<Properties>
				</Properties>
			</File>
			<File>
				FilePath="..\\User\\include\\FocControl.h"
				<Properties>
				</Properties>
			</File>
			<File>
				FilePath="..\\User\\include\\FocControlFunction.h"
				<Properties>
				</Properties>
			</File>
			<File>
				FilePath="..\\User\\include\\FOCTailDect.h"
				<Properties>
				</Properties>
			</File>
			<File>
				FilePath="..\\User\\include\\FU68xx_5_Flash.h"
				<Properties>
				</Properties>
			</File>
			<File>
				FilePath="..\\User\\include\\GPIO.H"
				<Properties>
				</Properties>
			</File>
			<File>
				FilePath="..\\User\\include\\I2C.H"
				<Properties>
				</Properties>
			</File>
			<File>
				FilePath="..\\User\\include\\IRScan.h"
				<Properties>
				</Properties>
			</File>
			<File>
				FilePath="..\\User\\include\\KeyScan.h"
				<Properties>
				</Properties>
			</File>
			<File>
				FilePath="..\\User\\include\\LED.h"
				<Properties>
				</Properties>
			</File>
			<File>
				FilePath="..\\User\\include\\MotorControl.h"
				<Properties>
				</Properties>
			</File>
			<File>
				FilePath="..\\User\\include\\MotorControlFunction.h"
				<Properties>
				</Properties>
			</File>
			<File>
				FilePath="..\\User\\include\\MyProject.h"
				<Properties>
				</Properties>
			</File>
			<File>
				FilePath="..\\User\\include\\PIInit.h"
				<Properties>
				</Properties>
			</File>
			<File>
				FilePath="..\\User\\include\\PosCheck.h"
				<Properties>
				</Properties>
			</File>
			<File>
				FilePath="..\\User\\include\\RSDDetect.h"
				<Properties>
				</Properties>
			</File>
			<File>
				FilePath="..\\User\\include\\SanityCheck.h"
				<Properties>
				</Properties>
			</File>
			<File>
				FilePath="..\\User\\include\\SMDU.h"
				<Properties>
				</Properties>
			</File>
			<File>
				FilePath="..\\User\\include\\SPI.H"
				<Properties>
				</Properties>
			</File>
			<File>
				FilePath="..\\User\\include\\Timer.H"
				<Properties>
				</Properties>
			</File>
			<File>
				FilePath="..\\User\\include\\TSD.H"
				<Properties>
				</Properties>
			</File>
			<File>
				FilePath="..\\User\\include\\UART.H"
				<Properties>
				</Properties>
			</File>
			<File>
				FilePath="..\\User\\include\\VoltageCompensation.h"
				<Properties>
				</Properties>
			</File>
		</Folder>
	</FilesTab>
	<FileConfigurationStructure>
		<File>
			ExtensionName="*"
			ExcludedFromBuild=""
		</File>
	</FileConfigurationStructure>
	<ResourcesTab>
	</ResourcesTab>
</FortiortechProject>
