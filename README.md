
# Standard Procedure for Hair Dryer

> 这是基于FU6562T的单电阻无感FOC吹风筒标准程序  
> 如有BUG或者优化建议，可提交到标准程序对应的议题

## 适用编译器

- [不支持] KeilC51
- [支持] FTCC

## 适用芯片
- FU6562T

## 发布记录
V1.1(2024-04-09)
- 修复七段式五段式切换，导致缺相问题
---  

V1.0(2024-03-15)
- 初始版本
- 基于FU6562T的单电阻无感FOC吹风筒标准程序
---

## 版权说明
> 版权所有©峰岹科技（深圳）股份有限公司（以下简称：峰岹科技）。<br>

> 为改进设计和/或性能，峰岹科技保留对本文档所描述或包含的产品（包括电路、标准元件和/><br>或软件）进行更改的权利。本文档中包含的信息供峰岹科技的客户进行一般性使用。峰岹科技的客户应确保采取适当行动，以使其对峰岹科技产品的使用不侵犯任何专利。峰岹科技尊重第三方的有效专利权，不侵犯或协助他人侵犯该等权利。<br>
> 本文档版权归峰岹科技所有，未经峰岹科技明确书面许可，任何单位及个人不得以任何形式或方式（如电子、机械、磁性、光学、化学、手工操作或其他任何方式），对本文档任何内容进行复制、传播、抄录、存储于检索系统或翻译为任何语种，亦不得更改或删除> 本内容副本中的任何版权或其他声明信息。<br>

> 峰岹科技（深圳）股份有限公司<br>
> 深圳市南山区科技中二路深圳软件园二期11栋2楼203<br>
> 邮编：518057<br>
> 电话：0755-26867710<br>
> 传真：0755-26867715<br>
> 网址：[www.fortiortech.com](https://www.fortiortech.com)

